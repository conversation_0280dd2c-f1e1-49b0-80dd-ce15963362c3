<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Text-to-CAD Chatbot</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <!-- Syntax highlighting -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <link rel="stylesheet" href="/static/css/styles.css" />
    <style>
      /* Chat bubble styles */
      .chat-bubble {
        max-width: 80%;
        word-wrap: break-word;
        animation: slideIn 0.3s ease-out;
      }

      .chat-bubble.user {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        margin-left: auto;
        border-radius: 18px 18px 4px 18px;
      }

      .chat-bubble.bot {
        background: #f8fafc;
        color: #374151;
        border: 1px solid #e5e7eb;
        margin-right: auto;
        border-radius: 18px 18px 18px 4px;
      }

      .chat-bubble.system {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #92400e;
        margin: 0 auto;
        border-radius: 18px;
        text-align: center;
        font-size: 0.875rem;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Typing indicator */
      .typing-indicator {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background: #f8fafc;
        border: 1px solid #e5e7eb;
        border-radius: 18px 18px 18px 4px;
        margin-right: auto;
        max-width: 80px;
      }

      .typing-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #9ca3af;
        margin: 0 2px;
        animation: typing 1.4s infinite ease-in-out;
      }

      .typing-dot:nth-child(1) {
        animation-delay: 0s;
      }
      .typing-dot:nth-child(2) {
        animation-delay: 0.2s;
      }
      .typing-dot:nth-child(3) {
        animation-delay: 0.4s;
      }

      @keyframes typing {
        0%,
        60%,
        100% {
          transform: translateY(0);
          opacity: 0.4;
        }
        30% {
          transform: translateY(-10px);
          opacity: 1;
        }
      }

      /* Chat input styles */
      .chat-input-container {
        background: white;
        border-radius: 25px;
        border: 2px solid #e5e7eb;
        transition: all 0.3s ease;
      }

      .chat-input-container:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .chat-input {
        border: none;
        outline: none;
        resize: none;
        background: transparent;
      }

      /* Progress animations */
      .progress-step {
        transition: all 0.3s ease;
      }

      .progress-step.active {
        background: #eff6ff;
        border-color: #3b82f6;
      }

      .progress-step.completed {
        background: #f0fdf4;
        border-color: #22c55e;
      }

      /* Mobile responsive */
      @media (max-width: 768px) {
        .chat-bubble {
          max-width: 90%;
        }

        .container {
          padding: 1rem;
        }
      }

      /* File upload area */
      .file-drop-zone {
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        transition: all 0.3s ease;
      }

      .file-drop-zone.drag-over {
        border-color: #667eea;
        background: #eff6ff;
      }

      /* Loading states */
      .btn-loading {
        position: relative;
        color: transparent !important;
      }

      .btn-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: button-spin 0.8s linear infinite;
      }

      @keyframes button-spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- Main Chat Interface -->
    <div class="container mx-auto px-4 py-6 max-w-6xl">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="flex items-center justify-center mb-4">
          <div
            class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4"
          >
            <i class="fas fa-robot text-white text-xl"></i>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-gray-800">
              Text-to-CAD Assistant
            </h1>
            <p class="text-gray-600">
              Generate 3D models from your descriptions
            </p>
          </div>
        </div>

        <!-- Status Bar -->
        <div
          class="flex items-center justify-center space-x-6 text-sm text-gray-500"
        >
          <div class="flex items-center">
            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            <span>Online</span>
          </div>
          <div id="session-indicator" class="hidden flex items-center">
            <i class="fas fa-link mr-2"></i>
            <span
              >Session: <span id="session-id-display" class="font-mono"></span
            ></span>
          </div>
          <div
            id="edit-mode-indicator"
            class="hidden flex items-center text-purple-600"
          >
            <i class="fas fa-pencil-alt mr-2"></i>
            <span>Edit Mode Active</span>
          </div>
        </div>
      </div>

      <!-- Main Chat Container -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Chat Area -->
        <div class="lg:col-span-2">
          <div
            class="bg-white rounded-2xl shadow-lg overflow-hidden h-[600px] flex flex-col"
          >
            <!-- Chat Header -->
            <div
              class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <i class="fas fa-comments mr-3"></i>
                  <div>
                    <h3 class="font-semibold">CAD Assistant</h3>
                    <p class="text-blue-100 text-sm">
                      Ready to help you create 3D models
                    </p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <label class="inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      id="edit-mode-toggle"
                      class="sr-only peer"
                    />
                    <div
                      class="relative w-11 h-6 bg-blue-400 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"
                    ></div>
                    <span class="ml-2 text-sm text-blue-100">Edit Mode</span>
                  </label>
                  <button
                    id="refresh-btn"
                    class="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                    title="New Chat"
                  >
                    <i class="fas fa-plus"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Chat Messages -->
            <div
              id="chat-messages"
              class="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50"
            >
              <!-- Welcome Message -->
              <div class="flex justify-center">
                <div class="chat-bubble system p-3 max-w-md">
                  <i class="fas fa-sparkles mr-2"></i>
                  Welcome! Describe a 3D model you'd like to create, or upload
                  an image/PDF for reference.
                </div>
              </div>
            </div>

            <!-- Chat Input -->
            <div class="p-4 bg-white border-t border-gray-200">
              <!-- File Upload Area -->
              <div id="selected-file-info" class="mb-3 empty:hidden"></div>

              <!-- Input Form -->
              <form
                id="chat-form"
                class="chat-input-container flex items-end p-2"
              >
                <div class="flex-1 relative">
                  <textarea
                    id="user-input"
                    placeholder="Describe your CAD model or ask a question..."
                    class="chat-input w-full p-3 text-sm max-h-32 min-h-[44px] resize-none"
                    rows="1"
                  ></textarea>
                </div>
                <div class="flex items-center space-x-2 ml-2">
                  <button
                    type="button"
                    id="attach-file-btn"
                    class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Attach file"
                  >
                    <i class="fas fa-paperclip"></i>
                  </button>
                  <button
                    type="submit"
                    id="send-btn"
                    class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled
                  >
                    <i class="fas fa-paper-plane"></i>
                  </button>
                </div>
              </form>

              <!-- Input Hints -->
              <div
                class="flex items-center justify-between text-xs text-gray-500 mt-2"
              >
                <div class="flex items-center space-x-4">
                  <span
                    ><i class="fas fa-keyboard mr-1"></i>Type description</span
                  >
                  <span
                    ><i class="fas fa-image mr-1"></i>Paste image (Ctrl+V)</span
                  >
                  <span><i class="fas fa-file mr-1"></i>Drag & drop files</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span id="char-count">0</span>
                  <span>•</span>
                  <span>JPG, PNG, PDF (max 20MB)</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Progress Panel -->
          <div
            id="processing-progress-section"
            class="bg-white rounded-2xl shadow-lg overflow-hidden"
          >
            <div
              class="bg-gradient-to-r from-green-500 to-teal-600 text-white p-4"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <i class="fas fa-cogs mr-3"></i>
                  <div>
                    <h3 class="font-semibold">Processing</h3>
                    <p class="text-green-100 text-sm">
                      CAD generation progress
                    </p>
                  </div>
                </div>
                <div
                  id="overall-progress-percentage"
                  class="text-2xl font-bold"
                >
                  0%
                </div>
              </div>
            </div>

            <div class="p-4 space-y-4">
              <!-- Progress Steps -->
              <div
                class="progress-step border border-gray-200 rounded-lg p-3"
                id="step-analysis"
              >
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3"
                    id="step-icon-analysis"
                  >
                    <i class="fas fa-search text-gray-500"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Analysis</p>
                    <p class="text-xs text-gray-500" id="step-status-analysis">
                      Waiting...
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="progress-step border border-gray-200 rounded-lg p-3"
                id="step-collection"
              >
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3"
                    id="step-icon-collection"
                  >
                    <i class="fas fa-list-alt text-gray-500"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Parameters</p>
                    <p
                      class="text-xs text-gray-500"
                      id="step-status-collection"
                    >
                      Waiting...
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="progress-step border border-gray-200 rounded-lg p-3"
                id="step-generation"
              >
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3"
                    id="step-icon-generation"
                  >
                    <i class="fas fa-code text-gray-500"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Generation</p>
                    <p
                      class="text-xs text-gray-500"
                      id="step-status-generation"
                    >
                      Waiting...
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="progress-step border border-gray-200 rounded-lg p-3"
                id="step-export"
              >
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3"
                    id="step-icon-export"
                  >
                    <i class="fas fa-file-export text-gray-500"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Export</p>
                    <p class="text-xs text-gray-500" id="step-status-export">
                      Waiting...
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="progress-step border border-gray-200 rounded-lg p-3"
                id="step-complete"
              >
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3"
                    id="step-icon-complete"
                  >
                    <i class="fas fa-check-circle text-gray-500"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Complete</p>
                    <p class="text-xs text-gray-500" id="step-status-complete">
                      Waiting...
                    </p>
                  </div>
                </div>
              </div>

              <!-- Overall Progress Bar -->
              <div class="mt-4">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Overall Progress</span>
                  <span id="overall-progress-text">0 of 5 steps</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    id="overall-progress-bar"
                    class="bg-gradient-to-r from-green-500 to-teal-600 h-2 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Results Panel -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div
              class="bg-gradient-to-r from-purple-500 to-pink-600 text-white p-4"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <i class="fas fa-cube mr-3"></i>
                  <div>
                    <h3 class="font-semibold">3D Model</h3>
                    <p class="text-purple-100 text-sm">Generated results</p>
                  </div>
                </div>
                <button
                  id="view-step-btn"
                  class="hidden px-3 py-1 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors text-sm"
                >
                  <i class="fas fa-external-link-alt mr-1"></i>View
                </button>
              </div>
            </div>

            <div class="p-4">
              <div
                id="code-output"
                class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto text-sm"
              >
                <div
                  class="flex flex-col items-center justify-center h-full text-gray-400"
                >
                  <i class="fas fa-cube text-3xl mb-3"></i>
                  <p class="text-center text-sm">
                    Generated code and 3D model will appear here
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div class="p-4 border-b border-gray-200">
              <h3 class="font-semibold text-gray-800 flex items-center">
                <i class="fas fa-bolt mr-2 text-yellow-500"></i>
                Quick Actions
              </h3>
            </div>
            <div class="p-4 space-y-3">
              <button
                class="quick-action-btn w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                data-action="box"
              >
                <div class="flex items-center">
                  <i class="fas fa-cube text-blue-500 mr-3"></i>
                  <div>
                    <p class="text-sm font-medium">Create Box</p>
                    <p class="text-xs text-gray-500">Simple rectangular box</p>
                  </div>
                </div>
              </button>
              <button
                class="quick-action-btn w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                data-action="cylinder"
              >
                <div class="flex items-center">
                  <i class="fas fa-circle text-green-500 mr-3"></i>
                  <div>
                    <p class="text-sm font-medium">Create Cylinder</p>
                    <p class="text-xs text-gray-500">Cylindrical shape</p>
                  </div>
                </div>
              </button>
              <button
                class="quick-action-btn w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                data-action="cone"
              >
                <div class="flex items-center">
                  <i class="fas fa-play text-purple-500 mr-3"></i>
                  <div>
                    <p class="text-sm font-medium">Create Cone</p>
                    <p class="text-xs text-gray-500">Conical shape</p>
                  </div>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Chatbot Toggle (for smaller screens) -->
    <div id="mobile-chat-toggle" class="lg:hidden fixed bottom-6 right-6 z-50">
      <button
        class="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg flex items-center justify-center hover:shadow-xl transition-all"
      >
        <i class="fas fa-robot text-xl"></i>
      </button>
    </div>

    <!-- Hidden elements for compatibility -->
    <div id="chat-toggle" class="hidden"></div>
    <div id="chatbot-widget" class="hidden"></div>
    <div id="chat-history" class="hidden"></div>

    <script src="/static/js/main.js"></script>
  </body>
</html>
