"""
CRUD operations for session-related models.
"""
from sqlalchemy.orm import Session
from fastapi import HTTPException
from sqlalchemy.sql import func
from sqlalchemy import or_
from typing import Optional
import random
import uuid
import logging

# Set up logger
logger = logging.getLogger(__name__)

try:
    from ..models.sessions import Session as SessionModel, ChatHistory
    from ..schemas.sessions import ChatRequest, ChatResponse
    from ..core.text_to_cad_agent import TextToCADAgent
except ImportError:
    # Fallback for direct imports
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from src.models.sessions import Session as SessionModel, ChatHistory
    from src.schemas.sessions import ChatRequest, ChatResponse
    from src.core.text_to_cad_agent import TextToCADAgent


# Get all sessions
def get_all_sessions(db: Session):
    """
    Get all sessions sorted by ID in descending order (newest first).

    Args:
        db (Session): Database session.

    Returns:
        list: List of all sessions sorted by ID in descending order.
    """
    return db.query(SessionModel).order_by(SessionModel.id.desc()).all()


# Create new session
def create_session(db: Session, session_id: str, name: str = "User"):
    """
    Create a new session.

    Args:
        db (Session): Database session.
        session_id (str): Session ID.
        name (str, optional): Session name. Defaults to "User".

    Returns:
        SessionModel: Created session.

    Raises:
        HTTPException: If session ID already exists.
    """
    # Check if session ID already exists
    existing_session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()

    if existing_session:
        # If session ID already exists, return error
        raise HTTPException(status_code=400, detail="Session ID already exists.")

    # Create new session
    new_session = SessionModel(session_id=session_id, name=name)
    db.add(new_session)
    db.commit()
    db.refresh(new_session)
    return new_session


# Get specific session
def get_session_by_id(db: Session, session_id: str):
    """
    Get a specific session by ID.

    Args:
        db (Session): Database session.
        session_id (str): Session ID.

    Returns:
        SessionModel: Session with the specified ID.
    """
    return db.query(SessionModel).filter(SessionModel.session_id == session_id).first()


# Soft delete session
def soft_delete_session(db: Session, session_id: str):
    """
    Soft delete a session.

    Args:
        db (Session): Database session.
        session_id (str): Session ID.

    Returns:
        SessionModel: Soft-deleted session.
    """
    # For now, just return the session (can be updated to set a status flag if needed)
    return db.query(SessionModel).filter(SessionModel.session_id == session_id).first()


# Hard delete session
def hard_delete_session(db: Session, session_id: str):
    """
    Hard delete a session.

    Args:
        db (Session): Database session.
        session_id (str): Session ID.

    Returns:
        SessionModel: Deleted session.

    Raises:
        HTTPException: If session not found.
    """
    session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
    if session:
        db.delete(session)
        db.commit()
        return session
    raise HTTPException(status_code=404, detail="Session not found")


# Delete session (soft or hard)
def delete_session(db: Session, session_id: str, deletion_type: str = "soft"):
    """
    Delete a session (soft or hard).

    Args:
        db (Session): Database session.
        session_id (str): Session ID.
        deletion_type (str, optional): Deletion type ("soft" or "hard"). Defaults to "soft".

    Returns:
        dict: Success message.

    Raises:
        HTTPException: If session not found or invalid deletion type.
    """
    if deletion_type == "soft":
        session = soft_delete_session(db, session_id)
        if session:
            return {"message": f"Session {session_id} has been soft deleted."}
        raise HTTPException(status_code=404, detail="Session not found")

    elif deletion_type == "hard":
        hard_delete_session(db, session_id)
        return {"message": f"Session {session_id} has been permanently deleted."}

    else:
        raise HTTPException(status_code=400, detail="Invalid deletion type. Use 'soft' or 'hard'.")


# Get chat history by session ID (paginated)
def get_chats_by_session_id(db: Session, session_id: str, page: int = 1, limit: int = 10):
    """
    Get chat history by session ID (paginated).

    Args:
        db (Session): Database session.
        session_id (str): Session ID.
        page (int, optional): Page number. Defaults to 1.
        limit (int, optional): Number of items per page. Defaults to 10.

    Returns:
        list: List of chat history entries.
    """
    offset = (page - 1) * limit
    return (
        db.query(ChatHistory)
        .filter(ChatHistory.session_id == session_id)
        .order_by(ChatHistory.id.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )


# Get exports by session ID
def get_exports_by_session_id(db: Session, session_id: str, export_format: str = None):
    """
    Get exports by session ID and convert local file paths to downloadable URLs.

    Args:
        db (Session): Database session.
        session_id (str): Session ID.
        export_format (str, optional): Export format filter. Defaults to None.

    Returns:
        list: List of downloadable URLs for the exported files.

    Raises:
        HTTPException: If no exports found.
    """
    # Import BASE_URL from main module
    import os
    from pathlib import Path

    # Get BASE_URL from environment or use default
    try:
        from ..api.main import BASE_URL
    except ImportError:
        # Fallback if import fails
        PORT = int(os.getenv("UVICORN_PORT", 8080))
        DOMAIN = os.getenv("DOMAIN", "http://localhost")

        # Only include port in BASE_URL if DOMAIN is localhost
        if DOMAIN == "http://localhost" or DOMAIN == "localhost":
            BASE_URL = f"{DOMAIN}:{PORT}"
        else:
            BASE_URL = DOMAIN

    logger.info(f"Using BASE_URL: {BASE_URL}")

    query = db.query(ChatHistory).filter(ChatHistory.session_id == session_id)

    # If export_format is provided, add filter
    if export_format:
        query = query.filter(ChatHistory.export_format == export_format)

    exports = query.all()

    if not exports:
        raise HTTPException(status_code=404, detail="No exports found for this session")

    # Convert local file paths to downloadable URLs
    downloadable_urls = []
    for export in exports:
        # Process OBJ exports
        if export.obj_export:
            # Check if the path is already a URL
            if export.obj_export.startswith(('http://', 'https://')):
                downloadable_urls.append({"format": "obj", "url": export.obj_export})
            else:
                # Convert local path to relative API URL
                # Format: http://localhost:8080/download/outputs/obj/YYYY-MM-DD/filename.obj

                # If the path contains the project root directory, extract only the part after it
                project_root = Path.cwd()
                project_root_str = str(project_root).replace('\\', '/')
                export_path_str = str(export.obj_export).replace('\\', '/')

                if project_root_str in export_path_str:
                    # Extract the part after the project root
                    relative_path = export_path_str.split(project_root_str, 1)[1].lstrip('\\/')
                    url = f"{BASE_URL}/download/{relative_path}"
                else:
                    url = f"{BASE_URL}/download/{export.obj_export}"

                downloadable_urls.append({"format": "obj", "url": url})

                # Log the URL for debugging
                logger.info(f"OBJ Export URL: {url}")

                # Check if the file exists
                file_path = Path(export.obj_export)
                full_path = project_root / file_path
                logger.info(f"OBJ file exists: {os.path.exists(full_path)}")

        # Process STEP exports
        if export.step_export:
            # Check if the path is already a URL
            if export.step_export.startswith(('http://', 'https://')):
                downloadable_urls.append({"format": "step", "url": export.step_export})
            else:
                # Convert local path to relative API URL
                # Format: http://localhost:8080/download/outputs/step/YYYY-MM-DD/filename.step

                # If the path contains the project root directory, extract only the part after it
                project_root = Path.cwd()
                project_root_str = str(project_root).replace('\\', '/')
                export_path_str = str(export.step_export).replace('\\', '/')

                if project_root_str in export_path_str:
                    # Extract the part after the project root
                    relative_path = export_path_str.split(project_root_str, 1)[1].lstrip('\\/')
                    url = f"{BASE_URL}/download/{relative_path}"
                else:
                    url = f"{BASE_URL}/download/{export.step_export}"

                downloadable_urls.append({"format": "step", "url": url})

                # Log the URL for debugging
                logger.info(f"STEP Export URL: {url}")

                # Check if the file exists
                file_path = Path(export.step_export)
                full_path = project_root / file_path
                logger.info(f"STEP file exists: {os.path.exists(full_path)}")

    return downloadable_urls


# Get tessellation by session ID
def get_tessellation_by_session_id(db: Session, session_id: str):
    """
    Get tessellation by session ID.

    Args:
        db (Session): Database session.
        session_id (str): Session ID.

    Returns:
        dict: Tessellation data.

    Raises:
        HTTPException: If no tessellation found.
    """
    # This is a placeholder - implement actual tessellation retrieval logic
    return {"vertices": [0, 1, 2], "faces": [0, 1, 2]}


# Handle chat request
def handle_chat_request(db: Session, chat_req: ChatRequest, agent: TextToCADAgent, request_origin: str = 'api'):
    """
    Handle chat request.

    Args:
        db (Session): Database session.
        chat_req (ChatRequest): Chat request.
        agent (TextToCADAgent): The TextToCAD agent instance.
        request_origin (str): The origin of the request ('web', 'api', or 'unknown').

    Returns:
        ChatResponse: Chat response.
    """
    # Enhanced session ID continuity management
    session_id = chat_req.session_id
    found_existing_session = False

    # Strategy 1: Check if message mentions an existing session ID
    if not session_id and chat_req.message:
        import re
        session_pattern = re.compile(r'session_[a-f0-9]{6}_\d{6}')
        session_matches = session_pattern.findall(chat_req.message)
        if session_matches:
            potential_session_id = session_matches[0]
            # Verify this session exists in database
            existing_session = db.query(SessionModel).filter(SessionModel.session_id == potential_session_id).first()
            if existing_session:
                session_id = potential_session_id
                chat_req.session_id = session_id
                found_existing_session = True

    # Strategy 2: If first strategy failed, check if this message is a follow-up to a recent question
    if not found_existing_session and not session_id:
        # Get recent chat history entries that asked questions (most recent first)
        # Also include entries from image processing that might need follow-up
        recent_question_sessions = db.query(ChatHistory)\
            .filter(
                or_(
                    ChatHistory.output.like('%Missing parameters:%'),
                    ChatHistory.output.like('%questions:%'),
                    ChatHistory.message.like('%Based on image analysis:%')
                )
            )\
            .order_by(ChatHistory.id.desc())\
            .limit(10)\
            .all()

        for entry in recent_question_sessions:
            # If the current message appears to be answering the questions from this session
            question_output = entry.output

            # Extract parameters from the question
            import re
            params_match = re.search(r'Missing parameters: ([^\]]+)', question_output)
            if params_match:
                param_list = params_match.group(1).split(',')

                # Enhanced parameter matching - check for various patterns
                message_lower = chat_req.message.lower()

                # Check if current message addresses any of these parameters
                for param in param_list:
                    param_clean = param.strip().lower()

                    # Direct parameter name match
                    if param_clean in message_lower:
                        session_id = entry.session_id
                        chat_req.session_id = session_id
                        found_existing_session = True
                        break

                    # Enhanced matching for common dimension patterns
                    if any(keyword in param_clean for keyword in ['length', 'width', 'dimension']):
                        # Look for dimension patterns like "1000x500", "1000 x 500", "length: 1000", etc.
                        dimension_patterns = [
                            r'\d+\s*x\s*\d+',  # 1000x500, 1000 x 500
                            r'\d+\s*\*\s*\d+',  # 1000*500, 1000 * 500
                            r'length[:\s]\s*\d+',  # length: 1000, length 1000
                            r'width[:\s]\s*\d+',   # width: 500, width 500
                            r'size[:\s]\s*\d+',    # size: 1000, size 1000
                            r'\d+\s*mm',           # 1000mm
                            r'\d+\s*cm',           # 100cm
                            r'\d+\s*m(?:\s|$)',    # 1m (with space or end of string)
                        ]

                        if any(re.search(pattern, message_lower) for pattern in dimension_patterns):
                            session_id = entry.session_id
                            chat_req.session_id = session_id
                            found_existing_session = True
                            logger.info(f"Matched follow-up message '{chat_req.message}' to existing session {session_id} based on dimension pattern")
                            break

                    # Enhanced matching for thickness parameters
                    if any(keyword in param_clean for keyword in ['thickness', 'height', 'depth']):
                        thickness_patterns = [
                            r'\d+\.?\d*\s*mm',     # 1.5mm, 2mm
                            r'\d+\.?\d*\s*cm',     # 1.5cm
                            r'thick[:\s]\s*\d+',   # thick: 1.5, thick 1.5
                            r'thickness[:\s]\s*\d+', # thickness: 1.5
                            r'\d+\.?\d*$',         # Just a number like "1.5"
                        ]

                        if any(re.search(pattern, message_lower) for pattern in thickness_patterns):
                            session_id = entry.session_id
                            chat_req.session_id = session_id
                            found_existing_session = True
                            logger.info(f"Matched follow-up message '{chat_req.message}' to existing session {session_id} based on thickness pattern")
                            break

                    # Enhanced matching for hole-related parameters
                    if any(keyword in param_clean for keyword in ['hole', 'perforation', 'pattern']):
                        hole_patterns = [
                            r'hole[:\s]\s*\w+',     # hole: round, hole square
                            r'pattern[:\s]\s*\w+',  # pattern: square, pattern round
                            r'perforation[:\s]\s*\w+', # perforation: round
                            r'(round|square|circular|rectangular)', # shape words
                            r'\d+\s*mm\s*(hole|diameter)', # 5mm hole, 10mm diameter
                        ]

                        if any(re.search(pattern, message_lower) for pattern in hole_patterns):
                            session_id = entry.session_id
                            chat_req.session_id = session_id
                            found_existing_session = True
                            logger.info(f"Matched follow-up message '{chat_req.message}' to existing session {session_id} based on hole pattern")
                            break

                if found_existing_session:
                    break

            # Additional heuristic: If message is very short and contains only numbers/dimensions,
            # it's likely a follow-up to a recent question
            if not found_existing_session and len(chat_req.message.strip()) < 50:
                # Check if message looks like dimensions or simple parameters
                simple_answer_patterns = [
                    r'^\d+\s*x\s*\d+$',        # Just "1000x500"
                    r'^\d+\.?\d*$',             # Just a number "1.5"
                    r'^\d+\s*mm$',              # Just "5mm"
                    r'^(round|square|circular|rectangular)$', # Just shape words
                    r'^\w{1,20}$',              # Single short word
                    r'.*pattern.*',             # Contains "pattern"
                    r'.*straight.*',            # Contains "straight"
                ]

                if any(re.search(pattern, chat_req.message.strip(), re.IGNORECASE) for pattern in simple_answer_patterns):
                    # This looks like a simple answer, link to most recent question
                    session_id = entry.session_id
                    chat_req.session_id = session_id
                    found_existing_session = True
                    logger.info(f"Matched simple answer '{chat_req.message}' to most recent question session {session_id}")
                    break

        # Strategy 3: If still no match, try to match with the most recent session that has missing info
        if not found_existing_session and not session_id:
            # Get the most recent session that has missing parameters or questions
            # Prioritize sessions from the last 10 minutes
            from datetime import datetime, timedelta

            # First try to find very recent sessions (last 10 minutes)
            # Prioritize image analysis sessions first
            ten_minutes_ago = datetime.now() - timedelta(minutes=10)

            # Priority 1: Recent image analysis sessions
            recent_session = db.query(ChatHistory)\
                .filter(ChatHistory.message.like('%Based on image analysis:%'))\
                .filter(ChatHistory.created_at >= ten_minutes_ago)\
                .order_by(ChatHistory.id.desc())\
                .first()

            # Priority 2: Recent sessions with missing parameters
            if not recent_session:
                recent_session = db.query(ChatHistory)\
                    .filter(
                        or_(
                            ChatHistory.output.like('%Missing parameters:%'),
                            ChatHistory.output.like('%questions:%')
                        )
                    )\
                    .filter(ChatHistory.created_at >= ten_minutes_ago)\
                    .order_by(ChatHistory.id.desc())\
                    .first()

            # If no very recent session, fall back to any recent session
            # Priority 3: Any recent image analysis session
            if not recent_session:
                recent_session = db.query(ChatHistory)\
                    .filter(ChatHistory.message.like('%Based on image analysis:%'))\
                    .order_by(ChatHistory.id.desc())\
                    .first()

            # Priority 4: Any recent session with missing parameters
            if not recent_session:
                recent_session = db.query(ChatHistory)\
                    .filter(
                        or_(
                            ChatHistory.output.like('%Missing parameters:%'),
                            ChatHistory.output.like('%questions:%')
                        )
                    )\
                    .order_by(ChatHistory.id.desc())\
                    .first()

            if recent_session:
                session_id = recent_session.session_id
                chat_req.session_id = session_id
                found_existing_session = True
                logger.info(f"Matched follow-up message '{chat_req.message}' to most recent session with questions: {session_id}")

    # If session_id is None or an empty string (from Pydantic default), create a new one.
    if not session_id or session_id == "": # Explicitly check for empty string
        rand_digits = random.randint(100000, 999999)
        rand_uuid = uuid.uuid4().hex[:6]
        new_generated_id = f"session_{rand_uuid}_{rand_digits}"
        session_id = new_generated_id # Update local session_id
        chat_req.session_id = new_generated_id # IMPORTANT: Update the DTO's session_id as well

    # Find session if exists, create if not
    session_db_model = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
    if not session_db_model:
        # Use a more descriptive name if possible, or default
        session_name = chat_req.message[:50].strip() if chat_req.message else "User Session"
        session_db_model = SessionModel(session_id=session_id, name=session_name)
        db.add(session_db_model)
        db.commit()
        db.refresh(session_db_model)

    # Process the request using the TextToCADAgent
    # Assuming is_edit_request is False for this endpoint, or get it from chat_req if available
    agent_result = agent.process_request(
        user_text=chat_req.message,
        is_edit_request=False, # Assuming new chat requests are not edits by default via this CRUD
        request_origin=request_origin,
        session_id=session_id # Ensure the local, authoritative session_id is passed to the agent
    )

    # Process the request:
    # 1. If parameters are missing: chat_response will be a message, obj_export will be empty
    # 2. If parameters are sufficient: chat_response will be code, obj_export will be the obj file path

    # Check if additional information is required
    missing_parameters = False
    chat_response_content = None
    obj_export_path = None

    # If there is a "message" field and no "code" field, then additional parameters are required
    if agent_result.get("message") and not agent_result.get("code"):
        missing_parameters = True
        chat_response_content = agent_result.get("message")
    # If there is an error, treat it as missing parameters
    elif agent_result.get("error"):
        missing_parameters = True
        chat_response_content = f"Error: {agent_result.get('error')}"
    # Case where parameters are sufficient and code is available
    elif agent_result.get("code"):
        missing_parameters = False
        # Create success response without including the code
        chat_response_content = "FreeCAD code generation successful."
        # Save code separately for use in the database
        code_content = agent_result.get("code")
        obj_export_path = agent_result.get("obj_path")

    # Default values for other fields
    tessellated_data = None
    attr_trans_map = None
    mfg_errors = []

    # Get export paths from agent_result based on export_format
    obj_export_path = None
    step_export_path = None

    # Logic for export format:
    # - None or empty: Export both OBJ and STEP
    # - "obj": Export only OBJ
    # - "step": Export only STEP
    export_format = chat_req.export_format

    # Debug logging
    logger.info(f"[DEBUG] export_format received: '{export_format}'")
    logger.info(f"[DEBUG] agent_result keys: {list(agent_result.keys())}")
    logger.info(f"[DEBUG] obj_path from agent: {agent_result.get('obj_path')}")
    logger.info(f"[DEBUG] step_path from agent: {agent_result.get('step_path')}")

    if export_format is None or export_format == "":
        # Export both formats
        obj_export_path = agent_result.get("obj_path")
        step_export_path = agent_result.get("step_path")
        logger.info(f"[SUCCESS] Export format not specified - exporting both OBJ and STEP")
        logger.info(f"   [FILE] OBJ path: {obj_export_path}")
        logger.info(f"   [FILE] STEP path: {step_export_path}")
    elif export_format.lower() == "obj":
        # Export only OBJ
        obj_export_path = agent_result.get("obj_path")
        logger.info(f"[SUCCESS] Export format: OBJ only")
        logger.info(f"   [FILE] OBJ path: {obj_export_path}")
    elif export_format.lower() == "step":
        # Export only STEP
        step_export_path = agent_result.get("step_path")
        logger.info(f"[SUCCESS] Export format: STEP only")
        logger.info(f"   [FILE] STEP path: {step_export_path}")
    else:
        # Default to both if unknown format
        obj_export_path = agent_result.get("obj_path")
        step_export_path = agent_result.get("step_path")
        logger.warning(f"[WARNING] Unknown export format '{export_format}' - defaulting to both OBJ and STEP")
        logger.info(f"   [FILE] OBJ path: {obj_export_path}")
        logger.info(f"   [FILE] STEP path: {step_export_path}")

    add_chat_history_entry(
        db=db,
        session_id=session_id,
        user_message=chat_req.message,
        agent_result=agent_result, # Pass the raw agent_result
        chat_request_obj=chat_req, # Pass the original ChatRequest object
        obj_export_path=obj_export_path
    )

    # Import BASE_URL from main module
    import os
    from pathlib import Path

    # Get BASE_URL from environment or use default
    try:
        from ..api.main import BASE_URL
    except ImportError:
        # Fallback if import fails
        PORT = int(os.getenv("UVICORN_PORT", 8080))
        DOMAIN = os.getenv("DOMAIN", "http://localhost")

        # Only include port in BASE_URL if DOMAIN is localhost
        if DOMAIN == "http://localhost" or DOMAIN == "localhost":
            BASE_URL = f"{DOMAIN}:{PORT}"
        else:
            BASE_URL = DOMAIN

    # Convert export paths to downloadable URLs based on what was requested
    obj_downloadable_url = None
    step_downloadable_url = None

    logger.info(f"[LINK] Creating download URLs...")
    logger.info(f"   [FILE] OBJ path to process: {obj_export_path}")
    logger.info(f"   [FILE] STEP path to process: {step_export_path}")

    # Only create URLs for the formats that were actually exported
    if obj_export_path:
        # Format: http://localhost:8080/download/outputs/obj/YYYY-MM-DD/filename.obj
        project_root = Path.cwd()
        project_root_str = str(project_root).replace('\\', '/')
        export_path_str = str(obj_export_path).replace('\\', '/')

        if project_root_str in export_path_str:
            # Extract the part after the project root
            relative_path = export_path_str.split(project_root_str, 1)[1].lstrip('\\/')
            obj_downloadable_url = f"{BASE_URL}/download/{relative_path}"
        else:
            obj_downloadable_url = f"{BASE_URL}/download/{obj_export_path}"

        logger.info(f"[SUCCESS] Created OBJ downloadable URL: {obj_downloadable_url}")
    else:
        logger.info(f"[ERROR] No OBJ path provided - skipping OBJ URL creation")

    if step_export_path:
        # Format: http://localhost:8080/download/outputs/step/YYYY-MM-DD/filename.step
        project_root = Path.cwd()
        project_root_str = str(project_root).replace('\\', '/')
        export_path_str = str(step_export_path).replace('\\', '/')

        if project_root_str in export_path_str:
            # Extract the part after the project root
            relative_path = export_path_str.split(project_root_str, 1)[1].lstrip('\\/')
            step_downloadable_url = f"{BASE_URL}/download/{relative_path}"
        else:
            step_downloadable_url = f"{BASE_URL}/download/{step_export_path}"

        logger.info(f"[SUCCESS] Created STEP downloadable URL: {step_downloadable_url}")
    else:
        logger.info(f"[ERROR] No STEP path provided - skipping STEP URL creation")

    # Return the chat response without including the code
    logger.info(f"[RESPONSE] Final response preparation:")
    logger.info(f"   [CHAT] chat_response: {chat_response_content}")
    logger.info(f"   [ID] session_id: {session_id}")
    logger.info(f"   [FILE] obj_export: {obj_downloadable_url}")
    logger.info(f"   [FILE] step_export: {step_downloadable_url}")

    response = ChatResponse(
        chat_response=chat_response_content, # Return the chatbot response without code
        session_id=session_id,
        obj_export=obj_downloadable_url,  # Return the full downloadable URL for OBJ
        step_export=step_downloadable_url,  # Return the full downloadable URL for STEP
        tessellated_export=tessellated_data,
        attribute_and_transientid_map=attr_trans_map,
        manufacturing_errors=mfg_errors
    )

    logger.info(f"[SUCCESS] Response created successfully")
    return response


def add_chat_history_entry(db: Session, session_id: str, user_message: str, agent_result: dict, chat_request_obj: Optional[ChatRequest] = None, obj_export_path: Optional[str] = None):
    """
    Adds a new entry to the chat history.

    Args:
        db (Session): Database session.
        session_id (str): The ID of the session.
        user_message (str): The message input by the user.
        agent_result (dict): The result dictionary from the TextToCADAgent.
        chat_request_obj (Optional[ChatRequest]): The original ChatRequest Pydantic model, if available.
                                                 Used to get details like image_path, part_file_name, etc.
        obj_export_path (Optional[str]): Path to the exported OBJ file, if any.
    """
    try:
        # Ensure session exists before adding chat history
        session_db_model = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
        if not session_db_model:
            # Create session if it doesn't exist
            session_name = user_message[:50].strip() if user_message else "User Session"
            session_db_model = SessionModel(session_id=session_id, name=session_name)
            db.add(session_db_model)
            db.commit()
            db.refresh(session_db_model)
            logger.info(f"Created new session {session_id} for chat history entry")

        chat_response_content = None
        if agent_result.get("message") and not agent_result.get("code"):
            chat_response_content = agent_result.get("message")
        elif agent_result.get("error"):
            chat_response_content = f"Error: {agent_result.get('error')}"
        elif agent_result.get("code"):
            chat_response_content = "FreeCAD code generation successful." # This is the message, not the code itself for the response

        # Determine the output to store in ChatHistory
        if agent_result.get("error"):
            chat_history_db_output = f"ERROR_RESPONSE: {agent_result.get('error')}"
        elif agent_result.get("code"):
            # Store the full code in the database if available, or a snippet
            code_to_store = agent_result.get('code', '')
            chat_history_db_output = f"CODE_GENERATED: {code_to_store}" # Storing full code, or adjust as needed
        elif agent_result.get("message"): # Typically questions or info
            chat_history_db_output = agent_result.get("message")
        else: # Fallback for unexpected agent_result structure
            chat_history_db_output = "Agent produced an unknown response structure."

        image_path = chat_request_obj.image_path if chat_request_obj else None
        part_file_name = chat_request_obj.part_file_name if chat_request_obj else "default_part"
        # Handle export_format: None means both formats were exported
        export_format = chat_request_obj.export_format if chat_request_obj and chat_request_obj.export_format else "both"
        material_choice = chat_request_obj.material_choice if chat_request_obj else "STEEL"
        selected_feature_uuid = chat_request_obj.selected_feature_uuid if chat_request_obj else None

        # Get STEP export path from agent_result if available
        step_export_path = agent_result.get("step_path")

        # Extract lasted_code from agent_result if available
        lasted_code = agent_result.get("code") if agent_result.get("code") else None

        new_chat_history_entry = ChatHistory(
            session_id=session_id,
            message=user_message, # This is the crucial part for storing image/pdf analysis text
            image_path=image_path,
            part_file_name=part_file_name,
            export_format=export_format,
            material_choice=material_choice,
            selected_feature_uuid=selected_feature_uuid,
            output=chat_history_db_output,
            obj_export=obj_export_path,
            step_export=step_export_path,
            lasted_code=lasted_code  # Store latest generated code
        )
        db.add(new_chat_history_entry)
        db.commit()
        db.refresh(new_chat_history_entry)
        return new_chat_history_entry

    except Exception as e:
        # Rollback the transaction on any error
        db.rollback()
        logger.error(f"Error adding chat history entry for session {session_id}: {e}")
        raise e


def update_session(db: Session, session_id: str, **kwargs):
    """
    Update session with provided fields. For lasted_code, updates the latest chat_history entry.

    Args:
        db (Session): Database session.
        session_id (str): Session ID to update.
        **kwargs: Fields to update (e.g., lasted_code, name).

    Returns:
        SessionModel or ChatHistory: Updated session/chat_history or None if not found.
    """
    try:
        # Handle lasted_code separately - store in chat_history
        if 'lasted_code' in kwargs:
            lasted_code = kwargs.pop('lasted_code')

            # Find the latest chat_history entry for this session
            latest_chat = db.query(ChatHistory).filter(
                ChatHistory.session_id == session_id
            ).order_by(ChatHistory.created_at.desc()).first()

            if latest_chat:
                # Update the latest chat_history entry with lasted_code
                latest_chat.lasted_code = lasted_code
                db.commit()
                db.refresh(latest_chat)
                logger.info(f"Updated latest chat_history for session {session_id} with lasted_code ({len(lasted_code)} characters)")
            else:
                logger.warning(f"No chat_history found for session {session_id} to store lasted_code")
                return None

        # Handle other session fields (like name)
        if kwargs:
            session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()

            if not session:
                logger.warning(f"Session {session_id} not found for update")
                return None

            # Update provided fields
            for field, value in kwargs.items():
                if hasattr(session, field):
                    setattr(session, field, value)
                    logger.info(f"Updated session {session_id} field '{field}'")
                else:
                    logger.warning(f"Field '{field}' not found in Session model")

            # Commit changes
            db.commit()
            db.refresh(session)
            logger.info(f"Successfully updated session {session_id}")
            return session

        # If only lasted_code was updated, return the chat_history entry
        return latest_chat

    except Exception as e:
        db.rollback()
        logger.error(f"Error updating session {session_id}: {e}")
        return None


def get_latest_code(db: Session, session_id: str):
    """
    Get the latest generated code for a session from chat_history.

    Args:
        db (Session): Database session.
        session_id (str): Session ID.

    Returns:
        str: Latest code or None if not found.
    """
    try:
        logger.info(f"🔍 [CRUD] Searching for latest code in session {session_id}")

        # Find the latest chat_history entry with lasted_code for this session
        latest_chat = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id,
            ChatHistory.lasted_code.isnot(None),
            ChatHistory.lasted_code != ''
        ).order_by(ChatHistory.created_at.desc()).first()

        if not latest_chat:
            logger.info(f"❌ [CRUD] No latest code found for session {session_id}")

            # Debug: Check if session has any chat history at all
            any_chat = db.query(ChatHistory).filter(
                ChatHistory.session_id == session_id
            ).count()
            logger.info(f"🔍 [CRUD] Session {session_id} has {any_chat} total chat entries")

            return None

        latest_code = latest_chat.lasted_code
        if latest_code:
            logger.info(f"✅ [CRUD] Retrieved latest code for session {session_id} from chat_history ({len(latest_code)} characters)")
            logger.info(f"🔍 [CRUD] Code preview: {latest_code[:100]}...")
            logger.info(f"📅 [CRUD] Code created at: {latest_chat.created_at}")
        else:
            logger.info(f"❌ [CRUD] No latest code found for session {session_id}")

        return latest_code

    except Exception as e:
        logger.error(f"Error getting latest code for session {session_id}: {e}")
        return None
