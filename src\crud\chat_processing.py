"""
CRUD operations for chat processing and request handling.
"""
from sqlalchemy.orm import Session
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Optional
import logging
import uuid
import random
import re
import json
import pprint

import asyncio # Added for async operations
from ..models.sessions import Session as SessionModel, ChatHistory
from ..schemas.sessions import ChatRequest, ChatResponse
from ..core.text_to_cad_agent import TextToCADAgent
from .sessions import create_session, get_session_by_id, update_session, get_latest_code, add_chat_history_entry
from ..utils.web_search_handler import WebSearchProcessor

logger = logging.getLogger(__name__)

# Initialize web search processor
web_search_processor = WebSearchProcessor()


def handle_chat_request(
    db: Session,
    chat_req: ChatRequest,
    agent,
    request_origin: str = 'api'
) -> ChatResponse:
    """
    Handle chat request with session management and web search integration.
    """
    # Debug: Print information being sent to chatbot
    print("\n" + "="*80)
    print(f"[DEBUG] CHATBOT REQUEST INFO (Origin: {request_origin})")
    print("-"*80)
    print(f"Message: {chat_req.message}")
    print(f"Session ID: {chat_req.session_id}")
    print(f"Is Edit Request: {chat_req.is_edit_request}")
    
    # Print additional fields if they exist and aren't empty
    if hasattr(chat_req, 'image_path') and chat_req.image_path:
        print(f"Image Path: {chat_req.image_path}")
    if hasattr(chat_req, 'part_file_name') and chat_req.part_file_name:
        print(f"Part File Name: {chat_req.part_file_name}")
    if hasattr(chat_req, 'export_format') and chat_req.export_format:
        print(f"Export Format: {chat_req.export_format}")
    if hasattr(chat_req, 'material_choice') and chat_req.material_choice:
        print(f"Material Choice: {chat_req.material_choice}")
    if hasattr(chat_req, 'selected_feature_uuid') and chat_req.selected_feature_uuid:
        print(f"Selected Feature UUID: {chat_req.selected_feature_uuid}")
    
    # Get session state to check for latest code
    session_id = _resolve_session_id(db, chat_req)
    latest_code = get_latest_code(db, session_id)
    if latest_code:
        print(f"Latest Code Available: Yes (Length: {len(latest_code)} characters)")
    else:
        print(f"Latest Code Available: No")
    
    # Get latest RAG context if available in agent
    try:
        state = agent._get_session_state(session_id)
        if 'latest_requirements' in state and state['latest_requirements']:
            print("-"*80)
            print("Latest RAG Context:")
            try:
                print(json.dumps(state['latest_requirements'].dict(), indent=2))
            except:
                print(pprint.pformat(state['latest_requirements'], indent=2))
    except Exception as e:
        print(f"Error accessing agent state: {str(e)}")
    
    print("="*80 + "\n")

    # Continue with normal processing
    # Ensure session exists
    session = get_session_by_id(db, session_id)
    if not session:
        session_name = chat_req.message[:50].strip() if chat_req.message else "User Session"
        session = create_session(db, session_id, session_name)

    # Update the chat request with the resolved session ID
    chat_req.session_id = session_id

    # Check if this is an edit request and sync latest code
    is_edit_request = chat_req.is_edit_request

    if is_edit_request:
        # Get latest code from database
        db_latest_code = get_latest_code(db, session_id)
        if db_latest_code:
            logger.info(f"Retrieved latest code from database for edit mode in session {session_id}")
            # Always sync database latest_code to TextToCADAgent session state for edit mode
            # This ensures we use the most recent code from database, not cached agent state
            agent._update_session_state(session_id, latest_code=db_latest_code)
            logger.info(f"Synced database latest_code to agent session state for {session_id}")
        else:
            logger.warning(f"Edit mode requested but no latest code found in database for session {session_id}")

    # Enhanced processing: Check for web search requirements
    processed_message = chat_req.message
    web_metadata = {}

    if web_search_processor and web_search_processor.is_web_search_request(chat_req.message):
        logger.info("[WEB] Web search request detected")

        # Process text with URLs
        has_urls, enhanced_text, metadata = web_search_processor.process_text_with_urls(chat_req.message)

        if has_urls:
            logger.info(f"[WEB] Processed {metadata['successful_extractions']}/{metadata['urls_found']} URLs successfully")
            processed_message = enhanced_text
            web_metadata = metadata
        else:
            logger.info("[WEB] No valid URLs found for web search")

    # Process the request using the TextToCAD agent with enhanced message
    agent_result = agent.process_request(
        user_text=processed_message,
        is_edit_request=is_edit_request,
        request_origin=request_origin,
        session_id=session_id
    )

    # Debug: Print response summary
    print("\n" + "="*80)
    print(f"[DEBUG] CHATBOT RESPONSE SUMMARY (Origin: {request_origin})")
    print("-"*80)
    if "code" in agent_result and agent_result["code"]:
        print(f"Generated Code: Yes (Length: {len(agent_result['code'])} characters)")
    else:
        print(f"Generated Code: No")
    
    if "message" in agent_result and agent_result["message"]:
        print(f"Response Message: {agent_result['message'][:200]}...")
    elif "error" in agent_result and agent_result["error"]:
        print(f"Error: {agent_result['error']}")
    
    if "obj_path" in agent_result and agent_result["obj_path"]:
        print(f"OBJ Export Path: {agent_result['obj_path']}")
    if "step_path" in agent_result and agent_result["step_path"]:
        print(f"STEP Export Path: {agent_result['step_path']}")
    if "gltf_path" in agent_result and agent_result["gltf_path"]:
        print(f"GLTF Export Path: {agent_result['gltf_path']}")
    print("="*80 + "\n")

    # Add web search metadata to agent result if available
    if web_metadata:
        agent_result["web_search_metadata"] = web_metadata
        logger.info(f"[WEB] Added web search metadata with {len(web_metadata.get('web_contents', []))} extracted contents")

    # Process the agent result and create response
    response = _process_agent_result(db, chat_req, agent_result, session_id)

    return response


def _resolve_session_id(db: Session, chat_req: ChatRequest) -> str:
    """
    Resolve session ID - create new if not provided.
    """
    session_id = chat_req.session_id

    # If session_id is provided and not empty, validate it exists
    if session_id and session_id != "":
        existing_session = db.query(SessionModel).filter(
            SessionModel.session_id == session_id
        ).first()
        if existing_session:
            return session_id
        else:
            # If session doesn't exist but session_id is provided (e.g., from PDF/Image processing),
            # use the provided session_id instead of creating a new one
            logger.warning(f"Provided session_id {session_id} does not exist, but will use it for continuity")
            return session_id

    # Check if message explicitly mentions an existing session ID
    if chat_req.message:
        session_pattern = re.compile(r'session_[a-f0-9]{6}_\d{6}')
        session_matches = session_pattern.findall(chat_req.message)
        if session_matches:
            potential_session_id = session_matches[0]
            existing_session = db.query(SessionModel).filter(
                SessionModel.session_id == potential_session_id
            ).first()
            if existing_session:
                logger.info(f"Found session ID {potential_session_id} mentioned in message")
                return potential_session_id

    # Generate completely new session ID
    new_session_id = _generate_session_id()
    logger.info(f"Created new session ID: {new_session_id}")
    return new_session_id


def _generate_session_id() -> str:
    """
    Generate a new unique session ID.
    """
    rand_digits = random.randint(100000, 999999)
    rand_uuid = uuid.uuid4().hex[:6]
    return f"session_{rand_uuid}_{rand_digits}"


def _process_agent_result(
    db: Session,
    chat_req: ChatRequest,
    agent_result: dict,
    session_id: str
) -> ChatResponse:
    """
    Process agent result and create chat response.
    """
    # Determine response content
    if agent_result.get("message") and not agent_result.get("code"):
        chat_response_content = agent_result.get("message")
    elif agent_result.get("error"):
        chat_response_content = f"Error: {agent_result.get('error')}"
    elif agent_result.get("code"):
        chat_response_content = "FreeCAD code generation successful."
    else:
        chat_response_content = "Processing completed."

    # Handle export paths
    obj_export_path, step_export_path = _handle_export_paths(chat_req, agent_result)

    # Add to chat history
    _add_to_chat_history(db, session_id, chat_req, agent_result, obj_export_path, step_export_path)

    # Create download URLs
    obj_url = _create_download_url(obj_export_path) if obj_export_path else None
    step_url = _create_download_url(step_export_path) if step_export_path else None

    return ChatResponse(
        chat_response=chat_response_content,
        session_id=session_id,
        obj_export=obj_url,
        step_export=step_url,
        tessellated_export=None,
        attribute_and_transientid_map=None,
        manufacturing_errors=[]
    )


def _handle_export_paths(chat_req: ChatRequest, agent_result: dict) -> tuple:
    """
    Handle export file paths based on export format.
    """
    export_format = chat_req.export_format
    obj_path = agent_result.get("obj_path")
    step_path = agent_result.get("step_path")

    if export_format is None or export_format == "":
        return obj_path, step_path
    elif export_format.lower() == "obj":
        return obj_path, None
    elif export_format.lower() == "step":
        return None, step_path
    else:
        return obj_path, step_path


def _add_to_chat_history(
    db: Session,
    session_id: str,
    chat_req: ChatRequest,
    agent_result: dict,
    obj_export_path: Optional[str],
    step_export_path: Optional[str]
):
    """
    Add entry to chat history.
    """
    # Determine output content
    if agent_result.get("error"):
        output = f"ERROR_RESPONSE: {agent_result.get('error')}"
    elif agent_result.get("code"):
        output = f"CODE_GENERATED: {agent_result.get('code')}"
    elif agent_result.get("message"):
        output = agent_result.get("message")
    else:
        output = "Agent produced an unknown response structure."

    # Extract lasted_code from agent_result
    lasted_code = agent_result.get("code") if agent_result.get("code") else None

    export_format = chat_req.export_format or "both"

    # Add to chat history with lasted_code
    add_chat_history_entry(
        db=db,
        session_id=session_id,
        user_message=chat_req.message,
        agent_result=agent_result,  # Pass the full agent_result dict
        chat_request_obj=chat_req,  # Pass the full ChatRequest object
        obj_export_path=obj_export_path
    )

    # Log success if code was generated and stored
    if lasted_code:
        logger.info(f"Stored latest code in chat_history for session {session_id} ({len(lasted_code)} characters)")


def _create_download_url(file_path: str) -> str:
    """
    Create download URL from file path.
    """
    import os
    BASE_URL = os.getenv("BASE_URL", "http://localhost:8080")
    return f"{BASE_URL}/download/{file_path}"


async def generate_cad_realtime_stream(
    db: Session,
    message: str,
    is_edit_request: bool,
    session_id: Optional[str],
    agent: TextToCADAgent
):
    """
    Asynchronously generates CAD and streams real-time progress updates.
    """
    steps = [
        {"id": "analysis", "name": "Analysis & Parameter Check", "icon_active": "fas fa-search fa-spin", "icon_complete": "fas fa-search"},
        {"id": "collection", "name": "Parameter Collection", "icon_active": "fas fa-list-alt fa-spin", "icon_complete": "fas fa-list-alt"},
        {"id": "generation", "name": "CAD Code Generation", "icon_active": "fas fa-code fa-spin", "icon_complete": "fas fa-code"},
        {"id": "export", "name": "File Export", "icon_active": "fas fa-file-export fa-spin", "icon_complete": "fas fa-file-export"},
        {"id": "complete", "name": "Complete", "icon_active": "fas fa-check-circle fa-spin", "icon_complete": "fas fa-check-circle"},
    ]
    total_steps = len(steps)

    logger.info(f"Starting real-time CAD generation stream for: '{message[:50]}...'")

    try:
        # STEP 1: Analysis & Parameter Check
        yield {
            "step": "analysis",
            "status": "Analyzing input and checking parameters...",
            "message": "Processing...",
            "icon": "fas fa-search fa-spin",
            "is_complete": False,
            "is_active": True,
            "overall_percentage": 0,
        }
        await asyncio.sleep(0.1)  # Ensure update is sent immediately

        # Resolve session ID
        resolved_session_id = _resolve_session_id(db, ChatRequest(message=message, session_id=session_id, is_edit_request=is_edit_request))

        # Ensure session exists
        session_obj = get_session_by_id(db, resolved_session_id)
        if not session_obj:
            session_name = message[:50].strip() if message else "User Session"
            create_session(db, resolved_session_id, session_name)
            logger.info(f"Created session {resolved_session_id} during stream processing")

        # Complete analysis step
        yield {
            "step": "analysis",
            "status": "Analysis completed.",
            "message": "Completed",
            "icon": "fas fa-search",
            "is_complete": True,
            "is_active": False,
            "overall_percentage": 20,
        }
        await asyncio.sleep(0.1)

        # STEP 2: Parameter Collection
        yield {
            "step": "collection",
            "status": "Collecting necessary parameters...",
            "message": "Processing...",
            "icon": "fas fa-list-alt fa-spin",
            "is_complete": False,
            "is_active": True,
            "overall_percentage": 20,
        }
        await asyncio.sleep(0.1)

        # Check for web search requirements
        processed_message = message
        web_metadata = {}

        if web_search_processor and web_search_processor.is_web_search_request(message):
            logger.info("[WEB] Web search request detected")
            has_urls, enhanced_text, metadata = web_search_processor.process_text_with_urls(message)
            if has_urls:
                processed_message = enhanced_text
                web_metadata = metadata

        # Complete parameter collection
        yield {
            "step": "collection",
            "status": "Parameter collection completed.",
            "message": "Completed",
            "icon": "fas fa-list-alt",
            "is_complete": True,
            "is_active": False,
            "overall_percentage": 40,
        }
        await asyncio.sleep(0.1)

        # STEP 3: CAD Code Generation
        yield {
            "step": "generation",
            "status": "Generating FreeCAD Python code...",
            "message": "Processing...",
            "icon": "fas fa-code fa-spin",
            "is_complete": False,
            "is_active": True,
            "overall_percentage": 40,
        }
        await asyncio.sleep(0.1)

        # Actually process the request using the agent
        agent_result = None
        async def run_agent_processing():
            nonlocal agent_result
            try:
                # Create a proper ChatRequest object
                chat_req_obj = ChatRequest(
                    message=processed_message,
                    is_edit_request=is_edit_request,
                    session_id=resolved_session_id
                )

                # Process using the agent
                agent_result = agent.process_request(
                    user_text=processed_message,
                    is_edit_request=is_edit_request,
                    request_origin='web',
                    session_id=resolved_session_id
                )

                # Add web search metadata if available
                if web_metadata:
                    agent_result["web_search_metadata"] = web_metadata

            except Exception as e:
                logger.error(f"Error in agent processing: {e}")
                agent_result = {"error": str(e)}

        # Run agent processing in background while showing progress
        agent_task = asyncio.create_task(run_agent_processing())

        # Show periodic updates while agent is working
        progress_counter = 0
        while not agent_task.done():
            await asyncio.sleep(0.5)
            progress_counter += 1

            # Update progress every 0.5 seconds
            yield {
                "step": "generation",
                "status": f"Generating code... ({progress_counter * 0.5:.1f}s)",
                "message": "Processing...",
                "icon": "fas fa-code fa-spin",
                "is_complete": False,
                "is_active": True,
                "overall_percentage": 40,
            }

            # Timeout after 30 seconds
            if progress_counter >= 60:
                agent_task.cancel()
                break

        # Wait for agent to complete
        try:
            await agent_task
        except asyncio.CancelledError:
            agent_result = {"error": "Code generation timed out"}

        # Complete code generation
        yield {
            "step": "generation",
            "status": "Code generation completed.",
            "message": "Completed",
            "icon": "fas fa-code",
            "is_complete": True,
            "is_active": False,
            "overall_percentage": 80,
        }
        await asyncio.sleep(0.1)

        # STEP 4: File Export
        yield {
            "step": "export",
            "status": "Exporting to STEP and OBJ formats...",
            "message": "Processing...",
            "icon": "fas fa-file-export fa-spin",
            "is_complete": False,
            "is_active": True,
            "overall_percentage": 80,
        }
        await asyncio.sleep(0.1)

        # Process the agent result and create response
        if agent_result and not agent_result.get("error"):
            chat_req_obj = ChatRequest(
                message=processed_message,
                is_edit_request=is_edit_request,
                session_id=resolved_session_id
            )
            final_response = _process_agent_result(db, chat_req_obj, agent_result, resolved_session_id)
        else:
            # Handle error case
            error_msg = agent_result.get("error", "Unknown error occurred") if agent_result else "Agent processing failed"
            final_response = ChatResponse(
                chat_response=f"Error: {error_msg}",
                session_id=resolved_session_id,
                obj_export=None,
                step_export=None,
                tessellated_export=None,
                attribute_and_transientid_map=None,
                manufacturing_errors=[]
            )

        # Complete export step
        yield {
            "step": "export",
            "status": "File export completed.",
            "message": "Completed",
            "icon": "fas fa-file-export",
            "is_complete": True,
            "is_active": False,
            "overall_percentage": 95,
        }
        await asyncio.sleep(0.1)

        # STEP 5: Complete
        yield {
            "step": "complete",
            "status": "All processing completed!",
            "message": "Completed",
            "icon": "fas fa-check-circle",
            "is_complete": True,
            "is_active": False,
            "overall_percentage": 100,
        }
        await asyncio.sleep(0.1)

        # Send final response
        yield {"final_response": final_response.model_dump()}
        logger.info(f"Real-time CAD generation completed for session {resolved_session_id}")

    except Exception as e:
        logger.error(f"Error in real-time CAD generation: {str(e)}")
        yield {"error": f"Failed to generate CAD: {str(e)}"}
